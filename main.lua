-- filename:
-- version: lua52
-- line: [0, 0] id: 0
nLog("开始")
require("TSLib")
thread = require("thread")
Server = require("httpServer")
thread.create(Server.start, {
  callBack = function()
    -- line: [7, 10] id: 1
    nLog("协程结束了", 0)
  end,
  errorBack = function(r0_2)
    -- line: [11, 14] id: 2
    nLog("协程错误了:" .. r0_2, 0)
  end,
  catchBack = function(r0_3)
    -- line: [15, 20] id: 3
    local r2_3 = require("ts").json
    nLog("协程异常了\n" .. json.encode(r0_3), 0)
  end,
})
for r3_0 = 1, 20, 1 do
  mSleep(100)
  if ServerPort then
    break
  end
end
option = {}
showWebUI({
  originx = option.originx,
  originy = option.originy,
  width = option.width,
  height = option.height,
  orient = option.orient,
  cornerRadius = option.cornerRadius,
  id = "WEBUI",
  url = "http://127.0.0.1:" .. ServerPort .. "/",
})
thread.waitAllThreadExit()
closeWebUI("WEBUI")
bool = writeFileString(userPath() .. "/res/config.json", 配置)
dialog(配置)
