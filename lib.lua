-- 调试打印函数，支持多种数据类型的格式化输出
function print2(...)
  local currentDate = os.date("%x")
  local args = {...}
  local argCount = #args

  if argCount == 0 then
    nLog("空值")
    return
  end

  local emptyStringPlaceholder = "空字符串"
  local emptyTablePlaceholder = "空表"
  local indentChar = "　"

  -- 格式化日志输出函数
  local function formatLog(...)
    local formatArgs = {...}
    nLog(string.format(table.unpack(formatArgs)))
  end

  -- 简单日志输出函数
  local function simpleLog(message)
    nLog(message)
    mSleep(10)
  end

  -- 递归打印表格内容的函数
  local function printTableRecursive(tableData, depth)
    mSleep(50)
    depth = (depth and depth + 1) or 1

    for key, value in pairs(tableData) do
      local valueType = type(value)
      local indent = string.rep("　　　　", depth)
      local keyStr = tostring(key)

      if valueType == "table" then
        if value._type then
          formatLog("%s[%s][%s]%s", indent, keyStr, valueType, tostring(value))
        else
          formatLog("%s[%s][%s]={", indent, keyStr, valueType)
          printTableRecursive(value, depth)
          formatLog("%s}", string.rep("　　　　　　　　", depth))
        end
      elseif valueType == "number" then
        formatLog("%s[%s][number] = %s", indent, keyStr, tonumber(value))
      elseif valueType == "string" then
        local displayValue = (value == "") and emptyStringPlaceholder or value
        formatLog("%s[%s][string] = %s", indent, keyStr, displayValue)
      elseif valueType == "boolean" then
        local boolStr = value and "true" or "false"
        formatLog("%s[%s][bool] = %s", indent, keyStr, boolStr)
      elseif valueType == "userdata" then
        formatLog("%s[%s][userdata] = %s", indent, keyStr, value)
      else
        simpleLog(indentChar .. keyStr .. ":" .. valueType)
      end
    end
    mSleep(50)
  end

  -- 处理主要的参数列表
  for i = 1, argCount do
    local arg = args[i]
    local argType = type(arg)

    if argType == "table" then
      if arg._type then
        formatLog("[%s] = %s", arg._type, tostring(arg))
      else
        simpleLog("Print Table={")
        printTableRecursive(arg)
        simpleLog("}")
        mSleep(50)
      end
    elseif argType == "string" then
      local displayValue = (arg == "") and emptyStringPlaceholder or arg
      simpleLog("[string]" .. displayValue)
    elseif argType == "boolean" then
      local boolStr = arg and "true" or "false"
      formatLog("%s%s", "Print boolean = ", boolStr)
    elseif argType == "number" then
      simpleLog("[number]" .. arg)
    elseif argType == "userdata" then
      formatLog("%s", arg)
    else
      simpleLog(arg)
    end
  end
end
